import hashlib
import hmac
import time
import json
import base64
import secrets
from Crypto.Cipher import AES
from Crypto.Random import get_random_bytes
import struct

class Extension1688SecretGenerator:
    def __init__(self):
        # 从代码中提取的常量
        self.app_key = "dfc62734abf1b2330e99f4c0d7efb0a7"
        self.version = "1.0.11"
        # 推测的密钥（需要从实际代码中提取）
        self.secret_key = "1688_extension_secret"
        self.digest_key = "1688_digest_key"
        
    def generate_uuid(self):
        """生成类似的UUID格式"""
        # 生成16字节随机数据
        random_bytes = secrets.token_bytes(12)
        # Base64编码并截取前16位
        uuid = base64.b64encode(random_bytes).decode('utf-8')[:16]
        return uuid
    
    def generate_token(self, uuid, timestamp=None):
        """
        生成token - 基于实际观察的格式
        """
        if timestamp is None:
            timestamp = int(time.time() * 1000)
            
        # 构建token数据
        token_data = {
            "uuid": uuid,
            "timestamp": timestamp,
            "version": self.version,
            "appkey": self.app_key
        }
        
        # 方法1: 直接加密JSON数据
        json_str = json.dumps(token_data, separators=(',', ':'))
        
        # 使用AES加密
        key = hashlib.md5(self.secret_key.encode()).digest()
        cipher = AES.new(key, AES.MODE_ECB)
        
        # 填充到16字节的倍数
        padded_data = self._pad_data(json_str.encode())
        encrypted = cipher.encrypt(padded_data)
        
        token = base64.b64encode(encrypted).decode('utf-8')
        return token
    
    def generate_digest_code(self, uuid, token, api_data=None):
        """
        生成digestCode - 基于实际观察的格式
        """
        # 构建摘要数据
        digest_parts = [
            uuid,
            token,
            self.version,
            str(int(time.time() * 1000))
        ]
        
        if api_data:
            if isinstance(api_data, dict):
                api_data = json.dumps(api_data, separators=(',', ':'))
            digest_parts.append(api_data)
        
        # 组合数据
        combined_data = "|".join(digest_parts)
        
        # 生成摘要
        digest = hmac.new(
            self.digest_key.encode(),
            combined_data.encode(),
            hashlib.sha256
        ).digest()
        
        # Base64编码
        digest_code = base64.b64encode(digest).decode('utf-8')
        return digest_code
    
    def generate_x_1688extension_secret(self, api_name, params=None, uuid=None):
        """
        生成x-1688extension-secret头部
        """
        if uuid is None:
            uuid = self.generate_uuid()
            
        # 生成token
        token = self.generate_token(uuid)
        
        # 生成digestCode
        api_data = {
            "api": api_name,
            "params": params or {}
        }
        digest_code = self.generate_digest_code(uuid, token, api_data)
        
        # 构建最终的secret
        secret_data = {
            "digestCode": digest_code,
            "token": token,
            "uuid": uuid,
            "version": self.version
        }
        
        return secret_data
    
    def _pad_data(self, data):
        """PKCS7填充"""
        pad_len = 16 - (len(data) % 16)
        return data + bytes([pad_len] * pad_len)
    
    def reverse_engineer_from_sample(self, sample_data):
        """
        基于提供的样本数据进行逆向分析
        """
        print("=== 样本数据分析 ===")
        
        # 分析digestCode
        digest_code = sample_data["digestCode"]
        print(f"DigestCode: {digest_code}")
        
        try:
            digest_bytes = base64.b64decode(digest_code)
            print(f"DigestCode解码长度: {len(digest_bytes)} bytes")
            print(f"DigestCode十六进制: {digest_bytes.hex()}")
        except Exception as e:
            print(f"DigestCode解码失败: {e}")
        
        # 分析token
        token = sample_data["token"]
        print(f"\nToken: {token}")
        print(f"Token长度: {len(token)}")
        
        try:
            token_bytes = base64.b64decode(token)
            print(f"Token解码长度: {len(token_bytes)} bytes")
            print(f"Token十六进制前32字节: {token_bytes[:32].hex()}")
        except Exception as e:
            print(f"Token解码失败: {e}")
        
        # 分析UUID
        uuid = sample_data["uuid"]
        print(f"\nUUID: {uuid}")
        print(f"UUID长度: {len(uuid)}")
        
        # 尝试不同的解码方式
        self._try_decode_patterns(sample_data)
    
    def _try_decode_patterns(self, sample_data):
        """尝试不同的解码模式"""
        print("\n=== 尝试解码模式 ===")
        
        # 模式1: 直接MD5
        test_string = f"{sample_data['uuid']}|{sample_data['version']}"
        md5_result = hashlib.md5(test_string.encode()).digest()
        md5_b64 = base64.b64encode(md5_result).decode()
        print(f"MD5模式1: {md5_b64}")
        
        # 模式2: SHA256
        sha256_result = hashlib.sha256(test_string.encode()).digest()
        sha256_b64 = base64.b64encode(sha256_result).decode()
        print(f"SHA256模式1: {sha256_b64}")
        
        # 模式3: HMAC-SHA256
        hmac_result = hmac.new(
            b"1688_key",
            test_string.encode(),
            hashlib.sha256
        ).digest()
        hmac_b64 = base64.b64encode(hmac_result).decode()
        print(f"HMAC-SHA256模式1: {hmac_b64}")

# 使用示例
def main():
    generator = Extension1688SecretGenerator()
    
    # 你提供的样本数据
    sample_data = {
        "digestCode": "AG2eDPQPyjdfrM1vaTBR0t/du0bu6ds1W+3YMDk2rME=",
        "token": "vmmOG9TcucBLltFM4HY+WTMJ/aYOBvp9b/PI3Ic7FRYDQXQMgyN5SV3uJhNDEcOGI/oja2vo6UPwMkI96wRpdkqY8QBBVFI3",
        "uuid": "ngQ9KupjYptNP2eo",
        "version": "1.0.11"
    }
    
    print("=== 1688扩展Secret逆向分析 ===")
    
    # 分析样本数据
    generator.reverse_engineer_from_sample(sample_data)
    
    print("\n=== 生成新的Secret ===")
    
    # 生成新的secret
    api_name = "mtop.1688.pc.plugin.user.login.get"
    params = {"data": "{}", "v": "1.0"}
    
    new_secret = generator.generate_x_1688extension_secret(api_name, params)
    
    print("生成的新Secret:")
    for key, value in new_secret.items():
        print(f"{key}: {value}")
    
    print("\n=== HTTP请求头格式 ===")
    secret_header = json.dumps(new_secret, separators=(',', ':'))
    print(f"x-1688extension-secret: {secret_header}")

if __name__ == "__main__":
    main()